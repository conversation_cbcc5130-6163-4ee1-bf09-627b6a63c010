{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "description": "Core domain types and interfaces representing the business model of the Beauty CRM system", "devDependencies": {"@types/node": "^22.15.29", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-domain-types", "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-domain-types' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}