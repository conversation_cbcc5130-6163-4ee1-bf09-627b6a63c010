{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "description": "Shared queue names and constants for Beauty CRM", "devDependencies": {"rimraf": "^6.0.1", "typescript": "^5.8.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-queue-names", "private": true, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-queue-names' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}